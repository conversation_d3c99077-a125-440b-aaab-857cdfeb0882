const News = require('../models/News');
const ActivityLog = require('../models/ActivityLog');

const createNews = async (req, res) => {
  try {
    console.log('Creating news - User:', req.user);
    console.log('Creating news - Body:', req.body);

    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required.' });
    }

    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied. Superadmin only.' });
    }

    const { title, content } = req.body;

    if (!title || !content) {
      return res.status(400).json({ message: 'Title and content are required.' });
    }

    // Handle both old format (string) and new format (object) for backward compatibility
    let newsTitle, newsContent;

    if (typeof title === 'string') {
      newsTitle = { en: title, ar: title };
    } else {
      newsTitle = title;
    }

    if (typeof content === 'string') {
      newsContent = { en: content, ar: content };
    } else {
      newsContent = content;
    }

    // Validate title and content structure for new format
    if (typeof title === 'object' && (!newsTitle.en || !newsTitle.ar)) {
      return res.status(400).json({ message: 'Title must include both English and Arabic versions.' });
    }

    if (typeof content === 'object' && (!newsContent.en || !newsContent.ar)) {
      return res.status(400).json({ message: 'Content must include both English and Arabic versions.' });
    }

    const news = new News({
      title: newsTitle,
      content: newsContent,
      isGlobal: true,
      createdBy: req.user._id || req.user.id,
    });

    const savedNews = await news.save();

    // Log activity
    try {
      await ActivityLog.create({
        userId: req.user._id || req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Created general news announcement',
        details: `Title: ${typeof newsTitle === 'object' ? newsTitle.en : newsTitle}`,
        ipAddress: req.ip || req.connection.remoteAddress
      });
    } catch (logError) {
      console.error('Error logging activity:', logError);
      // Don't fail the news creation if logging fails
    }

    console.log('News created:', savedNews);
    res.status(201).json(savedNews);
  } catch (error) {
    console.error('Error creating news:', error.message, error.stack);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const createSpecificNews = async (req, res) => {
  try {
    console.log('Creating specific news - User:', req.user);
    console.log('Creating specific news - Body:', req.body);

    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required.' });
    }

    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied. Superadmin only.' });
    }

    const { title, content, recipientType, recipientId } = req.body;

    if (!title || !content || !recipientType || !recipientId) {
      return res.status(400).json({ message: 'Title, content, recipient type, and recipient ID are required.' });
    }

    // Handle both old format (string) and new format (object) for backward compatibility
    let newsTitle, newsContent;

    if (typeof title === 'string') {
      newsTitle = { en: title, ar: title };
    } else {
      newsTitle = title;
    }

    if (typeof content === 'string') {
      newsContent = { en: content, ar: content };
    } else {
      newsContent = content;
    }

    // Validate title and content structure for new format
    if (typeof title === 'object' && (!newsTitle.en || !newsTitle.ar)) {
      return res.status(400).json({ message: 'Title must include both English and Arabic versions.' });
    }

    if (typeof content === 'object' && (!newsContent.en || !newsContent.ar)) {
      return res.status(400).json({ message: 'Content must include both English and Arabic versions.' });
    }

    const news = new News({
      title: newsTitle,
      content: newsContent,
      isGlobal: false,
      recipientType,
      recipientId,
      createdBy: req.user._id || req.user.id,
    });

    const savedNews = await news.save();

    // Log activity
    try {
      await ActivityLog.create({
        userId: req.user._id || req.user.id,
        userName: req.user.name || req.user.email || 'Unknown',
        userRole: req.user.role,
        action: 'Created specific news announcement',
        details: `Title: ${typeof newsTitle === 'object' ? newsTitle.en : newsTitle}, Recipient: ${recipientType} - ${recipientId}`,
        ipAddress: req.ip || req.connection.remoteAddress
      });
    } catch (logError) {
      console.error('Error logging activity:', logError);
      // Don't fail the news creation if logging fails
    }

    console.log('Specific news created:', savedNews);
    res.status(201).json(savedNews);
  } catch (error) {
    console.error('Error creating specific news:', error.message, error.stack);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const getNews = async (req, res) => {
  try {
    const { university } = req.query;
    const query = university ? { $or: [{ university }, { isGlobal: true }] } : { isGlobal: true };
    const news = await News.find(query).sort({ createdAt: -1 });
    console.log(`Fetched ${news.length} news items${university ? ` for university: ${university}` : ''}`);
    res.json(news);
  } catch (error) {
    console.error('Error fetching news:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAllNews = async (req, res) => {
  try {
    if (req.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Access denied. Superadmin only.' });
    }

    const news = await News.find({}).sort({ createdAt: -1 });
    console.log(`Fetched ${news.length} news items for superadmin`);
    res.json(news);
  } catch (error) {
    console.error('Error fetching all news:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = { createNews, createSpecificNews, getNews, getAllNews };
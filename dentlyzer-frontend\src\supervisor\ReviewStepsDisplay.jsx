import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaTimesCircle, FaCircle, FaClock, FaCheck, FaTimes, FaComment } from 'react-icons/fa';
import axios from 'axios';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const ReviewStepsDisplay = ({ reviewSteps, procedureType, reviewId, onStepUpdate }) => {
  const [loading, setLoading] = useState({});
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [selectedStep, setSelectedStep] = useState(null);
  const [comment, setComment] = useState('');

  if (!reviewSteps || reviewSteps.length === 0) {
    return (
      <div className="p-4 rounded-lg text-center" style={{ backgroundColor: '#f8f9fa' }}>
        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>
      </div>
    );
  }

  // Helper function to get step status
  const getStepStatus = (step) => {
    if (!step.completed) return 'not_started';
    if (step.supervisorStatus === 'approved') return 'approved';
    if (step.supervisorStatus === 'declined') return 'declined';
    return 'pending'; // Student completed but supervisor hasn't reviewed
  };

  // Helper function to get step color
  const getStepColor = (status) => {
    switch (status) {
      case 'approved': return colorPalette.accent; // Green
      case 'pending': return '#FF8C00'; // Orange
      case 'declined': return '#DC3545'; // Red
      default: return '#6C757D'; // Gray
    }
  };

  // Helper function to get step icon
  const getStepIcon = (status) => {
    switch (status) {
      case 'approved': return <FaCheckCircle className="h-5 w-5" />;
      case 'pending': return <FaClock className="h-5 w-5" />;
      case 'declined': return <FaTimesCircle className="h-5 w-5" />;
      default: return <FaCircle className="h-5 w-5" />;
    }
  };

  // Count steps by status
  const approvedSteps = reviewSteps.filter(step => getStepStatus(step) === 'approved').length;
  const pendingSteps = reviewSteps.filter(step => getStepStatus(step) === 'pending').length;
  const totalSteps = reviewSteps.length;
  const completionPercentage = Math.round((approvedSteps / totalSteps) * 100);

  // Handle step status update
  const handleStepUpdate = async (stepIndex, supervisorStatus, supervisorComment = '') => {
    if (!reviewId) return;

    setLoading(prev => ({ ...prev, [stepIndex]: true }));
    try {
      const token = localStorage.getItem('token');
      const response = await axios.put(
        `http://localhost:5000/api/reviews/${reviewId}/steps/${stepIndex}/status`,
        { supervisorStatus, supervisorComment },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (onStepUpdate) {
        onStepUpdate(response.data);
      }
    } catch (error) {
      console.error('Error updating step status:', error);
      alert('Failed to update step status. Please try again.');
    } finally {
      setLoading(prev => ({ ...prev, [stepIndex]: false }));
    }
  };

  // Handle comment modal
  const openCommentModal = (stepIndex, action) => {
    setSelectedStep({ index: stepIndex, action });
    setComment(reviewSteps[stepIndex]?.supervisorComment || '');
    setShowCommentModal(true);
  };

  const submitWithComment = async () => {
    if (selectedStep) {
      await handleStepUpdate(selectedStep.index, selectedStep.action, comment);
      setShowCommentModal(false);
      setSelectedStep(null);
      setComment('');
    }
  };

  return (
    <div className="rounded-lg overflow-hidden" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>
      <div className="p-4 border-b" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>
        <div className="flex justify-between items-center">
          <h3 className="text-md font-semibold" style={{ color: colorPalette.primary }}>
            {procedureType} Review Steps
          </h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-32 rounded-full h-2.5 mr-2" style={{ backgroundColor: '#e5e7eb' }}>
                <div
                  className="h-2.5 rounded-full"
                  style={{
                    width: `${completionPercentage}%`,
                    backgroundColor: colorPalette.accent
                  }}
                ></div>
              </div>
              <span className="text-sm font-medium" style={{ color: colorPalette.text }}>
                {approvedSteps}/{totalSteps} Approved
              </span>
            </div>
            {pendingSteps > 0 && (
              <span className="text-sm font-medium px-2 py-1 rounded-full" style={{
                backgroundColor: '#FF8C0020',
                color: '#FF8C00'
              }}>
                {pendingSteps} Pending
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="divide-y" style={{ borderColor: '#e5e7eb' }}>
        {reviewSteps.map((step, index) => {
          const status = getStepStatus(step);
          const stepColor = getStepColor(status);

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="p-4 flex items-start justify-between"
              style={{
                backgroundColor: status !== 'not_started' ? `${stepColor}10` : colorPalette.background
              }}
            >
              <div className="flex items-start flex-1">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <div style={{ color: stepColor }}>
                    {getStepIcon(status)}
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-sm" style={{
                    fontWeight: status !== 'not_started' ? '500' : 'normal',
                    color: status !== 'not_started' ? colorPalette.text : '#666666'
                  }}>
                    {step.description}
                  </p>
                  {step.supervisorComment && (
                    <p className="text-xs mt-1 italic" style={{ color: '#6C757D' }}>
                      Supervisor note: {step.supervisorComment}
                    </p>
                  )}
                  <div className="flex items-center mt-1">
                    <span className="text-xs px-2 py-1 rounded-full" style={{
                      backgroundColor: `${stepColor}20`,
                      color: stepColor
                    }}>
                      {status === 'not_started' ? 'Not Started' :
                       status === 'pending' ? 'Pending Review' :
                       status === 'approved' ? 'Approved' : 'Declined'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Supervisor Action Buttons - Only show for pending steps */}
              {status === 'pending' && reviewId && (
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => handleStepUpdate(index, 'approved')}
                    disabled={loading[index]}
                    className="flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors"
                    style={{
                      backgroundColor: colorPalette.accent,
                      color: 'white'
                    }}
                    title="Approve step"
                  >
                    <FaCheck className="h-3 w-3 mr-1" />
                    {loading[index] ? 'Approving...' : 'Approve'}
                  </button>
                  <button
                    onClick={() => openCommentModal(index, 'declined')}
                    disabled={loading[index]}
                    className="flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors"
                    style={{
                      backgroundColor: '#DC3545',
                      color: 'white'
                    }}
                    title="Decline step"
                  >
                    <FaTimes className="h-3 w-3 mr-1" />
                    Decline
                  </button>
                  <button
                    onClick={() => openCommentModal(index, 'approved')}
                    disabled={loading[index]}
                    className="flex items-center px-2 py-1 rounded-md text-xs font-medium transition-colors"
                    style={{
                      backgroundColor: '#6C757D',
                      color: 'white'
                    }}
                    title="Add comment"
                  >
                    <FaComment className="h-3 w-3" />
                  </button>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Comment Modal */}
      {showCommentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-90vw">
            <h3 className="text-lg font-semibold mb-4" style={{ color: colorPalette.primary }}>
              {selectedStep?.action === 'approved' ? 'Approve Step' : 'Decline Step'}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {selectedStep?.action === 'approved'
                ? 'Add an optional comment for this approval:'
                : 'Please provide a reason for declining this step:'}
            </p>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={selectedStep?.action === 'approved'
                ? 'Optional comment...'
                : 'Reason for declining...'}
              className="w-full p-3 border border-gray-300 rounded-md resize-none"
              rows="3"
            />
            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => {
                  setShowCommentModal(false);
                  setSelectedStep(null);
                  setComment('');
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={submitWithComment}
                className="px-4 py-2 text-sm font-medium text-white rounded-md transition-colors"
                style={{
                  backgroundColor: selectedStep?.action === 'approved' ? colorPalette.accent : '#DC3545'
                }}
              >
                {selectedStep?.action === 'approved' ? 'Approve' : 'Decline'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewStepsDisplay;

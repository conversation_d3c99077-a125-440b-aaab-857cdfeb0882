const ActivityLog = require('../models/ActivityLog');

// Helper function to get action description based on method and path
const getActionDescription = (method, path, body) => {
  const pathSegments = path.split('/').filter(segment => segment);
  
  // Remove 'api' from the beginning if present
  if (pathSegments[0] === 'api') {
    pathSegments.shift();
  }

  const resource = pathSegments[0] || 'unknown';
  const action = pathSegments[1] || '';
  const id = pathSegments[2] || '';

  switch (method.toUpperCase()) {
    case 'POST':
      if (path.includes('/login')) return 'User logged in';
      if (path.includes('/logout')) return 'User logged out';
      if (path.includes('/register')) return 'User registered';
      if (path.includes('/news')) return 'Created news announcement';
      if (path.includes('/universities')) return 'Created university';
      if (path.includes('/accounts')) return 'Created user account';
      if (path.includes('/patients')) return 'Created patient record';
      if (path.includes('/appointments')) return 'Created appointment';
      if (path.includes('/teeth-chart')) return 'Created teeth chart';
      if (path.includes('/reviews')) return 'Created review';
      return `Created ${resource}`;

    case 'PUT':
    case 'PATCH':
      if (path.includes('/password')) return 'Updated password';
      if (path.includes('/profile')) return 'Updated profile';
      if (path.includes('/universities')) return 'Updated university';
      if (path.includes('/accounts')) return 'Updated user account';
      if (path.includes('/patients')) return 'Updated patient record';
      if (path.includes('/appointments')) return 'Updated appointment';
      if (path.includes('/teeth-chart')) return 'Updated teeth chart';
      if (path.includes('/reviews')) return 'Updated review';
      return `Updated ${resource}`;

    case 'DELETE':
      if (path.includes('/universities')) return 'Deleted university';
      if (path.includes('/accounts')) return 'Deleted user account';
      if (path.includes('/patients')) return 'Deleted patient record';
      if (path.includes('/appointments')) return 'Deleted appointment';
      if (path.includes('/teeth-chart')) return 'Deleted teeth chart';
      if (path.includes('/reviews')) return 'Deleted review';
      return `Deleted ${resource}`;

    case 'GET':
      // Only log certain GET requests to avoid spam
      if (path.includes('/export')) return 'Exported data';
      if (path.includes('/download')) return 'Downloaded file';
      if (path.includes('/analytics')) return 'Viewed analytics';
      if (path.includes('/reports')) return 'Generated report';
      return null; // Don't log regular GET requests

    default:
      return `${method} ${resource}`;
  }
};

// Helper function to extract relevant details from request
const getActionDetails = (method, path, body, query) => {
  const details = [];

  // Add relevant body information
  if (body) {
    if (body.name) details.push(`Name: ${body.name}`);
    if (body.email) details.push(`Email: ${body.email}`);
    if (body.title) details.push(`Title: ${typeof body.title === 'object' ? body.title.en || body.title : body.title}`);
    if (body.role) details.push(`Role: ${body.role}`);
    if (body.university) details.push(`University: ${body.university}`);
    if (body.status) details.push(`Status: ${body.status}`);
    if (body.procedure) details.push(`Procedure: ${body.procedure}`);
  }

  // Add relevant query parameters
  if (query) {
    if (query.university) details.push(`University: ${query.university}`);
    if (query.status) details.push(`Status: ${query.status}`);
    if (query.type) details.push(`Type: ${query.type}`);
  }

  // Add path parameters
  const pathSegments = path.split('/').filter(segment => segment);
  if (pathSegments.length > 2) {
    const id = pathSegments[pathSegments.length - 1];
    if (id && id !== 'api' && !isNaN(id)) {
      details.push(`ID: ${id}`);
    }
  }

  return details.join(', ');
};

// Activity logging middleware
const activityLogger = async (req, res, next) => {
  // Skip logging for certain paths
  const skipPaths = [
    '/api/auth/verify',
    '/api/analytics',
    '/api/activity',
    '/favicon.ico',
    '/health',
    '/ping'
  ];

  const shouldSkip = skipPaths.some(path => req.path.includes(path)) || 
                   req.method === 'OPTIONS' ||
                   req.path.includes('/static/') ||
                   req.path.includes('/assets/');

  if (shouldSkip) {
    return next();
  }

  // Store original res.json to intercept response
  const originalJson = res.json;
  let responseData = null;
  let statusCode = null;

  res.json = function(data) {
    responseData = data;
    statusCode = res.statusCode;
    return originalJson.call(this, data);
  };

  // Continue with the request
  next();

  // Log activity after response is sent
  res.on('finish', async () => {
    try {
      // Only log if user is authenticated and action is meaningful
      if (!req.user) return;

      const action = getActionDescription(req.method, req.path, req.body);
      if (!action) return; // Skip if no meaningful action

      // Only log successful operations (2xx status codes) or specific errors
      if (statusCode >= 400 && statusCode !== 401 && statusCode !== 403) return;

      const details = getActionDetails(req.method, req.path, req.body, req.query);

      await ActivityLog.create({
        userId: req.user._id || req.user.id,
        userName: req.user.name || req.user.email || 'Unknown User',
        userRole: req.user.role,
        action: action,
        details: details || undefined,
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error logging activity:', error);
      // Don't throw error to avoid breaking the main request
    }
  });
};

module.exports = activityLogger;
